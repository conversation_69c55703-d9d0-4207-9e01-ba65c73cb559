.knowledgeSettings {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e0;
    }
}

.sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding: 12px 24px;
    border-bottom: 1px solid #f0f2f5;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: auto;
    height: auto;

    &::before {
        content: '📚';
        font-size: 16px;
        margin-right: 8px;
    }
}

.actionButton {
    font-size: 13px;
    color: #1890ff;
    padding: 0;
    height: auto;
    
    &:hover {
        color: #40a9ff;
    }
}

.settingsContent {
    padding: 24px;
}

.settingItem {
    margin-bottom: 20px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.settingControl {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.settingLabel {
    font-size: 14px;
    color: #262626;
    min-width: 60px;
    font-weight: 500;
}

.inputNumber {
    :global(.ant-input-number) {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        &:hover {
            border-color: #1890ff;
        }
        
        &:focus-within {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    }
}

.hintText {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
}

.tableSection {
    margin-top: 16px;
}

.knowledgeTable {
    border: 1px solid #e8e8e8;
    border-radius: 6px;

    :global(.ant-table-cell) {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    :global(.ant-table-thead > tr > th) {
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 600;
    }
    
    :global(.ant-table-tbody > tr:hover > td) {
        background: #f5f5f5;
    }
}
